<script lang="ts">
import type { NavigationMenuListProps } from 'reka-ui'
import { SsrNavigationMenuList } from '~/utils/ssr-navigation-menu'
import { useForwardProps } from '~/utils/bundle/reka-ui'

export interface MenuLinkTabProps extends NavigationMenuListProps {}
</script>

<script setup lang="ts">
const props = defineProps<MenuLinkTabProps>()

const forward = useForwardProps(props)
</script>

<template>
  <SsrNavigationMenuList
    v-bind="forward"
    class="flex list-none"
  >
    <slot />
  </SsrNavigationMenuList>
</template>
