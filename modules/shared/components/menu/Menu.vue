<script lang="ts">
import type { NavigationMenuRootEmits, NavigationMenuRootProps } from 'reka-ui'
import { SsrNavigationMenuRoot } from '~/utils/ssr-navigation-menu'
import { useForwardPropsEmits } from '~/utils/bundle/reka-ui'

export interface MenuProps extends NavigationMenuRootProps {}
export interface MenuEmits extends NavigationMenuRootEmits {}
</script>

<script setup lang="ts">
const props = defineProps<MenuProps>()
const emits = defineEmits<MenuEmits>()

const forward = useForwardPropsEmits(props, emits)
</script>

<template>
  <SsrNavigationMenuRoot v-bind="forward" class="relative z-[1] flex">
    <slot />
  </SsrNavigationMenuRoot>
</template>
