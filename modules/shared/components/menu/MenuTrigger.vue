<script lang="ts">
import type { NavigationMenuTriggerProps } from 'reka-ui'
import { SsrNavigationMenuTrigger } from '~/utils/ssr-navigation-menu'
import { useForwardProps } from '~/utils/bundle/reka-ui'

export interface MenuTriggerProps extends NavigationMenuTriggerProps {}
</script>

<script setup lang="ts">
const props = defineProps<MenuTriggerProps>()

const forward = useForwardProps(props)
</script>

<template>
  <SsrNavigationMenuTrigger
    v-bind="forward"
    class="text-muted-700 hover:text-muted-900 dark:text-muted-500 dark:hover:text-muted-100 hover:bg-muted-100 dark:hover:bg-muted-800 focus-visible:nui-focus group flex select-none items-center justify-between gap-1 rounded-md px-3 py-2 text-[15px] font-sans leading-none outline-none"
  >
    <slot />
  </SsrNavigationMenuTrigger>
</template>
