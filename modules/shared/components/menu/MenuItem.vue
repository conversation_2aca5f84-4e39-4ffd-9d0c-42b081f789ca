<script lang="ts">
import type { NavigationMenuItemProps } from 'reka-ui'
import { SsrNavigationMenuItem } from '~/utils/ssr-navigation-menu'
import { useForwardProps } from '~/utils/bundle/reka-ui'

export interface MenuContentProps extends NavigationMenuItemProps {}
</script>

<script setup lang="ts">
const props = defineProps<MenuContentProps>()

const forward = useForwardProps(props)
</script>

<template>
  <SsrNavigationMenuItem v-bind="forward">
    <slot />
  </SsrNavigationMenuItem>
</template>
