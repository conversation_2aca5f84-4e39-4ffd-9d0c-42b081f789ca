<script lang="ts">
import type { NavigationMenuContentEmits, NavigationMenuContentProps } from 'reka-ui'
import { SsrNavigationMenuContent } from '~/utils/ssr-navigation-menu'
import { useForwardPropsEmits } from '~/utils/bundle/reka-ui'

export interface MenuContentProps extends NavigationMenuContentProps {}
export interface MenuContentEmits extends NavigationMenuContentEmits {}
</script>

<script setup lang="ts">
const props = defineProps<MenuContentProps>()
const emits = defineEmits<MenuContentEmits>()

const forward = useForwardPropsEmits(props, emits)
</script>

<template>
  <SsrNavigationMenuContent
    v-bind="forward"
    class="absolute top-0 start-0 w-full sm:w-auto"
  >
    <slot />
  </SsrNavigationMenuContent>
</template>
