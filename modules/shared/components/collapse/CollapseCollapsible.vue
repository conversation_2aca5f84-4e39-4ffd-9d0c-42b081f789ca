<script lang="ts">
import type { CollapsibleRootEmits, CollapsibleRootProps } from 'reka-ui'
import { SsrCollapsibleRoot, SsrCollapsibleContent } from '~/utils/ssr-collapsible'
import { useForwardPropsEmits } from '~/utils/bundle/reka-ui'

export interface CollapseCollapsibleProps extends CollapsibleRootProps {}
export interface CollapseCollapsibleEmits extends CollapsibleRootEmits {}
</script>

<script setup lang="ts">
const props = defineProps<CollapseCollapsibleProps>()
const emits = defineEmits<CollapseCollapsibleEmits>()

const forward = useForwardPropsEmits(props, emits)
</script>

<template>
  <SsrCollapsibleRoot v-bind="forward" class="group w-full ">
    <slot name="trigger" />
    <SsrCollapsibleContent class="flex w-full flex-col overflow-hidden ps-4 transition-all">
      <slot />
    </SsrCollapsibleContent>
  </SsrCollapsibleRoot>
</template>
