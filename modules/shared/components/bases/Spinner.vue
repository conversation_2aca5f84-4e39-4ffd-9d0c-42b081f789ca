<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'danger' | 'muted'
}>()

const sizeClasses = computed(() => {
  switch (props.size) {
    case 'xs':
      return 'h-3 w-3'
    case 'sm':
      return 'h-4 w-4'
    case 'md':
      return 'h-6 w-6'
    case 'lg':
      return 'h-8 w-8'
    case 'xl':
      return 'h-12 w-12'
    default:
      return 'h-6 w-6'
  }
})

const colorClasses = computed(() => {
  switch (props.color) {
    case 'primary':
      return 'text-primary-500'
    case 'secondary':
      return 'text-secondary-500'
    case 'info':
      return 'text-info-500'
    case 'success':
      return 'text-success-500'
    case 'warning':
      return 'text-warning-500'
    case 'danger':
      return 'text-danger-500'
    case 'muted':
      return 'text-muted-400 dark:text-muted-500'
    default:
      return 'text-primary-500'
  }
})
</script>

<template>
  <div role="status" class="inline-block">
    <svg
      class="animate-spin" :class="[sizeClasses, colorClasses]"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        class="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        stroke-width="4"
      />
      <path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
    <span class="sr-only">Loading...</span>
  </div>
</template>
