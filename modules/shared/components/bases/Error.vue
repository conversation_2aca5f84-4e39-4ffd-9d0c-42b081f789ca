<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  /**
   * The error message to display
   */
  error?: string | string[]
  /**
   * Whether to show the error icon
   */
  showIcon?: boolean
  /**
   * The size of the error message
   */
  size?: 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  error: undefined,
  showIcon: true,
  size: 'sm',
})

const errors = computed(() => {
  if (!props.error)
    return []
  return Array.isArray(props.error) ? props.error : [props.error]
})
</script>

<template>
  <TransitionGroup
    enter-active-class="transition duration-200 ease-out"
    enter-from-class="transform -translate-y-1 opacity-0"
    enter-to-class="transform translate-y-0 opacity-100"
    leave-active-class="transition duration-200 ease-in"
    leave-from-class="transform translate-y-0 opacity-100"
    leave-to-class="transform -translate-y-1 opacity-0"
  >
    <div
      v-for="(message, index) in errors"
      :key="message + index"
      class="flex items-start gap-1 pt-1"
    >
      <Icon
        v-if="showIcon"
        name="lucide:alert-circle"
        class="text-danger-500 size-3.5 shrink-0"
        :class="{
          'mt-0.5': size === 'sm',
          'mt-1': size === 'md',
          'mt-1.5': size === 'lg',
        }"
      />
      <p
        class="text-danger-500 font-sans"
        :class="{
          'text-xs': size === 'sm',
          'text-sm': size === 'md',
          'text-base': size === 'lg',
        }"
      >
        {{ message }}
      </p>
    </div>
  </TransitionGroup>
</template>
