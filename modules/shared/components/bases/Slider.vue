<script setup lang="ts">
interface Props {
  /**
   * The label for the slider
   */
  label?: string
  /**
   * The current value of the slider
   */
  modelValue: number
  /**
   * The minimum value of the slider
   */
  min?: number
  /**
   * The maximum value of the slider
   */
  max?: number
  /**
   * The step size for the slider
   */
  step?: number
  /**
   * Whether the slider is disabled
   */
  disabled?: boolean
  /**
   * Help text to display below the slider
   */
  help?: string
  /**
   * Error message to display
   */
  error?: string
}

const props = withDefaults(defineProps<Props>(), {
  min: 0,
  max: 100,
  step: 1,
  disabled: false,
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: number): void
}>()

const displayValue = computed(() => {
  // Format the value to match the step precision
  const stepDecimals = props.step.toString().split('.')[1]?.length || 0
  return props.modelValue.toFixed(stepDecimals)
})

function handleInput(event: Event) {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', Number.parseFloat(target.value))
}
</script>

<template>
  <div class="w-full">
    <!-- Label and value display -->
    <div class="mb-2 flex items-center justify-between">
      <label
        v-if="label"
        class="block text-sm font-medium text-gray-700"
      >
        {{ label }}
      </label>
      <span class="text-sm text-gray-500">{{ displayValue }}</span>
    </div>

    <!-- Slider input -->
    <div class="relative">
      <input
        type="range"
        :value="modelValue"
        :min="min"
        :max="max"
        :step="step"
        :disabled="disabled"
        class="h-2 w-full cursor-pointer appearance-none rounded-lg bg-gray-200 accent-primary-500 disabled:cursor-not-allowed disabled:opacity-50"
        @input="handleInput"
      >
    </div>

    <!-- Help text -->
    <p
      v-if="help && !error"
      class="mt-2 text-sm text-gray-500"
    >
      {{ help }}
    </p>

    <!-- Error message -->
    <p
      v-if="error"
      class="mt-2 text-sm text-danger-500"
    >
      {{ error }}
    </p>
  </div>
</template>

<style scoped>
/* Custom slider styling */
input[type='range'] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

/* Thumb styles */
input[type='range']::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 16px;
  width: 16px;
  margin-top: -6px;
  background-color: var(--color-primary-500);
  border-radius: 50%;
  border: none;
  transition: background-color 0.2s ease-in-out;
}

input[type='range']::-moz-range-thumb {
  height: 16px;
  width: 16px;
  background-color: var(--color-primary-500);
  border-radius: 50%;
  border: none;
  transition: background-color 0.2s ease-in-out;
}

/* Track styles */
input[type='range']::-webkit-slider-runnable-track {
  height: 4px;
  background: var(--color-gray-200);
  border-radius: 2px;
}

input[type='range']::-moz-range-track {
  height: 4px;
  background: var(--color-gray-200);
  border-radius: 2px;
}

/* Focus styles */
input[type='range']:focus {
  outline: none;
}

input[type='range']:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 2px var(--color-primary-100);
}

input[type='range']:focus::-moz-range-thumb {
  box-shadow: 0 0 0 2px var(--color-primary-100);
}

/* Disabled styles */
input[type='range']:disabled::-webkit-slider-thumb {
  background-color: var(--color-gray-400);
}

input[type='range']:disabled::-moz-range-thumb {
  background-color: var(--color-gray-400);
}
</style>
