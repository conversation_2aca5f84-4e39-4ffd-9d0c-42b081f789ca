<script setup lang="ts">
import type { BaseProviderConfig } from '@shuriken-ui/nuxt'

const props = defineProps<{
  config?: BaseProviderConfig
  tooltip?: any
  toast?: any
}>()

// Extract reactive values to avoid proxy issues during SSR
const config = computed(() => ({
  dir: props.config?.dir || 'ltr',
  locale: props.config?.locale || 'en',
}))

const tooltip = computed(() => props.tooltip || {})
const toast = computed(() => props.toast || { position: 'top-center' })
</script>

<template>
  <BaseProviders
    :config="config"
    :tooltip="tooltip"
    :toast="toast"
  >
    <slot />
  </BaseProviders>
</template>
