<script setup lang="ts">
interface Props {
  /**
   * Whether the field is required
   */
  required?: boolean
  /**
   * The size of the label
   */
  size?: 'sm' | 'md' | 'lg'
  /**
   * Whether to show the required indicator
   */
  showRequired?: boolean
}

withDefaults(defineProps<Props>(), {
  required: false,
  size: 'md',
  showRequired: true,
})
</script>

<template>
  <label class="block">
    <span
      class="text-muted-800 dark:text-muted-100 block font-sans"
      :class="{
        'text-sm': size === 'sm',
        'text-base': size === 'md',
        'text-lg': size === 'lg',
      }"
    >
      <slot />
      <span
        v-if="required && showRequired"
        class="text-danger-500 ms-1"
        aria-hidden="true"
      >
        *
      </span>
    </span>
  </label>
</template>
