{"name": "app", "type": "module", "private": true, "scripts": {"prepare": "nuxt prepare", "dev": "nuxt dev --open", "build": "nuxt build", "generate": "nuxt generate", "typecheck": "nuxt typecheck", "clean": "rimraf .nuxt .output node_modules"}, "devDependencies": {"@iconify-json/fa6-brands": "^1.2.5", "@iconify-json/ph": "^1.2.2", "@iconify-json/simple-icons": "^1.2.30", "@nuxt/fonts": "^0.11.1", "@shuriken-ui/nuxt": "4.0.0-beta.4", "nuxt": "3.16.2", "tailwindcss": "^4.1.3", "typescript": "5.8.3"}}