export const useMobileNavOpen = () => useState('tairo-demo-mobile-nav-open', () => false)
export const useSearchOpen = () => useState('tairo-demo-search-open', () => false)
export const useColorSwitcherOpen = () => {
  const state = useState('tairo-demo-color-switcher-open', () => false)

  // Force reset to false on initialization to fix stuck modal issue
  if (import.meta.client) {
    state.value = false
  }

  return state
}
