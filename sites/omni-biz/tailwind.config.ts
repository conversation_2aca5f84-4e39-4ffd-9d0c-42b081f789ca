import type { Config } from 'tailwindcss'

export default {
  // Tailwind CSS 4.x configuration for Nuxt 3 with SSR support
  content: [
    './app/**/*.{js,ts,vue}',
    './components/**/*.{js,ts,vue}',
    './layouts/**/*.{js,ts,vue}',
    './pages/**/*.{js,ts,vue}',
    './plugins/**/*.{js,ts}',
    './examples/**/*.{js,ts,vue}',
    './modules/**/*.{js,ts,vue}',
    // Include shared module content
    '../../modules/shared/**/*.{js,ts,vue}',
    // Include Shuriken UI components
    './node_modules/@shuriken-ui/**/*.{js,ts,vue}',
  ],
  
  // SSR environment context configuration
  experimental: {
    // Enable SSR support for Tailwind CSS 4.x
    optimizeUniversalDefaults: true,
  },
  
  // Theme configuration (moved from CSS @theme block)
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        mono: [
          'Fira Code',
          'ui-monospace',
          'SFMono-Regular',
          'Menlo',
          'Monaco',
          'Consolas',
          'Liberation Mono',
          'Courier New',
          'monospace',
        ],
        alt: ['Karla', 'sans-serif'],
      },
      
      animation: {
        'spin-slow': 'spin 3s linear infinite',
        'spin-fast': 'spin 0.65s linear infinite',
      },
      
      colors: {
        primary: {
          50: 'rgb(var(--color-primary-50) / <alpha-value>)',
          100: 'rgb(var(--color-primary-100) / <alpha-value>)',
          200: 'rgb(var(--color-primary-200) / <alpha-value>)',
          300: 'rgb(var(--color-primary-300) / <alpha-value>)',
          400: 'rgb(var(--color-primary-400) / <alpha-value>)',
          500: 'rgb(var(--color-primary-500) / <alpha-value>)',
          600: 'rgb(var(--color-primary-600) / <alpha-value>)',
          700: 'rgb(var(--color-primary-700) / <alpha-value>)',
          800: 'rgb(var(--color-primary-800) / <alpha-value>)',
          900: 'rgb(var(--color-primary-900) / <alpha-value>)',
          950: 'rgb(var(--color-primary-950) / <alpha-value>)',
        },
        chart: {
          base: 'rgb(var(--color-chart-base) / <alpha-value>)',
          gradient: 'rgb(var(--color-chart-gradient) / <alpha-value>)',
          title: 'rgb(var(--color-chart-title) / <alpha-value>)',
          subtitle: 'rgb(var(--color-chart-subtitle) / <alpha-value>)',
        },
      },
    },
  },
  
  plugins: [
    // Typography plugin
    require('@tailwindcss/typography'),
  ],
} satisfies Config
