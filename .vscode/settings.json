{"editor.formatOnSave": false, "editor.defaultFormatter": "dbaeumer.vscode-eslint", "editor.codeActionsOnSave": {"source.fixAll.eslint": "never"}, "[markdown]": {"editor.rulers": [80]}, "eslint.validate": ["javascript", "javascriptreact", "markdown", "typescript", "vue"], "tailwindCSS.experimental.configFile": ".demo/tailwind.config.ts", "typescript.tsdk": "node_modules/typescript/lib", "prettier.enable": false, "vue.codeActions.enabled": false, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[vue]": {"editor.defaultFormatter": "Vue.volar"}}